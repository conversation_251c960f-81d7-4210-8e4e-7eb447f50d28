"""
Тестовый скрипт для проверки регистрации обработчиков куратора
"""
import asyncio
import logging
from unittest.mock import AsyncMock, MagicMock
from aiogram import Router

# Настройка логирования
logging.basicConfig(level=logging.INFO)

async def test_curator_handlers_registration():
    """Тест регистрации обработчиков куратора"""
    print("🧪 Тестирование регистрации обработчиков куратора...")
    
    try:
        # Создаем мок роутер
        router = Router()
        
        # Импортируем состояния куратора
        from curator.states.states_tests import CuratorTestsStatisticsStates
        
        # Импортируем функцию регистрации
        from common.tests_statistics.register_handlers import register_test_statistics_handlers
        
        print("✅ Импорты успешны")
        
        # Проверяем наличие новых состояний
        assert hasattr(CuratorTestsStatisticsStates, 'month_entry_detailed_microtopics')
        assert hasattr(CuratorTestsStatisticsStates, 'month_entry_summary_microtopics')
        print("✅ Новые состояния микротем найдены")
        
        # Регистрируем обработчики
        register_test_statistics_handlers(router, CuratorTestsStatisticsStates, "curator")
        print("✅ Обработчики зарегистрированы без ошибок")
        
        # Проверяем количество зарегистрированных обработчиков
        handlers_count = len(router.callback_query.handlers)
        print(f"✅ Зарегистрировано {handlers_count} обработчиков callback_query")
        
        # Проверяем, что есть обработчики для микротем
        microtopics_handlers = [
            handler for handler in router.callback_query.handlers 
            if 'microtopics' in str(handler.callback.__name__) or 'back_from' in str(handler.callback.__name__)
        ]
        print(f"✅ Найдено {len(microtopics_handlers)} обработчиков микротем")
        
        # Выводим названия обработчиков микротем
        for handler in microtopics_handlers:
            print(f"   - {handler.callback.__name__}")
        
        print("🎉 Все тесты регистрации прошли успешно!")
        
    except Exception as e:
        print(f"❌ Ошибка в тестах регистрации: {e}")
        import traceback
        traceback.print_exc()

async def test_curator_adapter_integration():
    """Тест интеграции адаптера куратора"""
    print("\n🧪 Тестирование интеграции адаптера куратора...")
    
    try:
        # Создаем мок объекты
        callback = MagicMock()
        callback.from_user = MagicMock()
        callback.from_user.id = 12345
        callback.data = "month_entry_detailed_1_2_3"
        callback.message = MagicMock()
        callback.message.edit_text = AsyncMock()
        
        state = MagicMock()
        state.get_state = AsyncMock(return_value="CuratorTestsStatisticsStates:month_entry_result")
        state.update_data = AsyncMock()
        state.set_state = AsyncMock()
        
        # Импортируем обработчики
        from common.tests_statistics.handlers import (
            show_month_entry_detailed_microtopics,
            show_month_entry_summary_microtopics
        )
        
        print("✅ Импорт обработчиков успешен")
        
        # Мокаем адаптер
        from unittest.mock import patch
        
        with patch('common.tests_statistics.handlers.show_curator_month_entry_microtopics_with_images') as mock_adapter:
            mock_adapter.return_value = None
            
            # Тестируем обработчик детальной статистики
            await show_month_entry_detailed_microtopics(callback, state)
            print("✅ Обработчик детальной статистики работает")
            
            # Проверяем, что адаптер был вызван с правильными параметрами
            mock_adapter.assert_called_once()
            args, kwargs = mock_adapter.call_args
            
            assert kwargs['group_id'] == 1
            assert kwargs['month_test_id'] == 2
            assert kwargs['student_id'] == 3
            assert kwargs['display_mode'] == 'detailed'
            print("✅ Адаптер вызван с правильными параметрами для детальной статистики")
            
            # Тестируем обработчик сводки
            callback.data = "month_entry_summary_1_2_3"
            mock_adapter.reset_mock()
            
            await show_month_entry_summary_microtopics(callback, state)
            print("✅ Обработчик сводки работает")
            
            # Проверяем параметры для сводки
            mock_adapter.assert_called_once()
            args, kwargs = mock_adapter.call_args
            assert kwargs['display_mode'] == 'summary'
            print("✅ Адаптер вызван с правильным режимом 'summary'")
        
        print("🎉 Все тесты интеграции адаптера прошли успешно!")
        
    except Exception as e:
        print(f"❌ Ошибка в тестах интеграции: {e}")
        import traceback
        traceback.print_exc()

async def main():
    """Главная функция тестирования"""
    print("🚀 Запуск тестов обработчиков куратора\n")
    
    await test_curator_handlers_registration()
    await test_curator_adapter_integration()
    
    print("\n🎯 Тестирование завершено!")
    print("\n📋 Резюме:")
    print("✅ Созданы новые состояния для микротем куратора")
    print("✅ Зарегистрированы обработчики пагинации и возврата")
    print("✅ Адаптер корректно интегрирован с обработчиками")
    print("✅ Система готова к работе!")

if __name__ == "__main__":
    asyncio.run(main())
