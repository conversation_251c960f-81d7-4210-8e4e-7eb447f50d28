"""
Тестовый скрипт для проверки интеграции системы отображения микротем куратора
"""
import asyncio
import logging
from unittest.mock import AsyncMock, MagicMock

# Настройка логирования
logging.basicConfig(level=logging.INFO)

async def test_curator_adapter():
    """Тест адаптера куратора"""
    print("🧪 Тестирование адаптера куратора...")
    
    try:
        # Импортируем функции
        from common.microtopics.curator_adapter import (
            convert_curator_callback_to_student_format,
            show_curator_month_entry_microtopics_with_images
        )
        
        print("✅ Импорт адаптера успешен")
        
        # Создаем мок объекты
        callback = MagicMock()
        callback.message = MagicMock()
        callback.message.edit_text = AsyncMock()
        callback.message.answer_photo = AsyncMock()
        
        state = MagicMock()
        state.get_data = AsyncMock(return_value={})
        state.update_data = AsyncMock()
        state.set_state = AsyncMock()
        
        # Тестируем конвертацию (без реальной БД)
        print("🔄 Тестирование конвертации форматов...")
        
        # Мокаем репозиторий
        from unittest.mock import patch
        
        with patch('common.microtopics.curator_adapter.MonthEntryTestResultRepository') as mock_repo:
            # Мокаем результат
            mock_result = MagicMock()
            mock_result.id = 123
            mock_repo.get_by_student_and_month_test.return_value = mock_result
            
            # Тестируем конвертацию
            test_result_id = await convert_curator_callback_to_student_format(1, 2, 3)
            print(f"✅ Конвертация: group_id=1, month_test_id=2, student_id=3 -> test_result_id={test_result_id}")
            
            # Тестируем отображение (с моком)
            print("🖼️ Тестирование отображения изображений...")
            
            with patch('common.microtopics.curator_adapter.show_month_entry_test_microtopics_universal') as mock_show:
                mock_show.return_value = None
                
                from curator.states.states_tests import CuratorTestsStatisticsStates
                
                await show_curator_month_entry_microtopics_with_images(
                    callback=callback,
                    state=state,
                    group_id=1,
                    month_test_id=2,
                    student_id=3,
                    target_state=CuratorTestsStatisticsStates.month_entry_detailed_microtopics,
                    display_mode="detailed"
                )
                
                print("✅ Отображение изображений успешно")
                
                # Проверяем, что функция была вызвана
                mock_show.assert_called_once()
                print("✅ Универсальная функция отображения была вызвана")
        
        print("🎉 Все тесты адаптера прошли успешно!")
        
    except Exception as e:
        print(f"❌ Ошибка в тестах адаптера: {e}")
        import traceback
        traceback.print_exc()

async def test_states():
    """Тест состояний куратора"""
    print("\n🧪 Тестирование состояний куратора...")
    
    try:
        from curator.states.states_tests import CuratorTestsStatisticsStates
        
        # Проверяем новые состояния
        assert hasattr(CuratorTestsStatisticsStates, 'month_entry_detailed_microtopics')
        assert hasattr(CuratorTestsStatisticsStates, 'month_entry_summary_microtopics')
        
        print("✅ Новые состояния микротем добавлены")
        
        # Проверяем, что состояния разные
        detailed_state = CuratorTestsStatisticsStates.month_entry_detailed_microtopics
        summary_state = CuratorTestsStatisticsStates.month_entry_summary_microtopics
        
        assert detailed_state != summary_state
        print("✅ Состояния детальной и сводной статистики различаются")
        
        print("🎉 Все тесты состояний прошли успешно!")
        
    except Exception as e:
        print(f"❌ Ошибка в тестах состояний: {e}")
        import traceback
        traceback.print_exc()

async def test_handlers():
    """Тест обработчиков"""
    print("\n🧪 Тестирование обработчиков...")
    
    try:
        from common.tests_statistics.handlers import (
            show_month_entry_detailed_microtopics,
            show_month_entry_summary_microtopics
        )
        
        print("✅ Импорт обработчиков успешен")
        
        # Создаем мок объекты
        callback = MagicMock()
        callback.from_user = MagicMock()
        callback.from_user.id = 12345
        callback.data = "month_entry_detailed_1_2_3"
        callback.message = MagicMock()
        callback.message.edit_text = AsyncMock()
        
        state = MagicMock()
        state.get_state = AsyncMock(return_value="CuratorTestsStatisticsStates:month_entry_result")
        state.update_data = AsyncMock()
        state.set_state = AsyncMock()
        
        # Мокаем адаптер
        with patch('common.tests_statistics.handlers.show_curator_month_entry_microtopics_with_images') as mock_adapter:
            mock_adapter.return_value = None
            
            # Тестируем обработчик детальной статистики
            await show_month_entry_detailed_microtopics(callback, state)
            print("✅ Обработчик детальной статистики работает")
            
            # Проверяем, что адаптер был вызван
            mock_adapter.assert_called_once()
            print("✅ Адаптер был вызван из обработчика")
            
            # Тестируем обработчик сводки
            callback.data = "month_entry_summary_1_2_3"
            mock_adapter.reset_mock()
            
            await show_month_entry_summary_microtopics(callback, state)
            print("✅ Обработчик сводки работает")
            
            # Проверяем, что адаптер был вызван с правильным режимом
            mock_adapter.assert_called_once()
            args, kwargs = mock_adapter.call_args
            assert kwargs['display_mode'] == 'summary'
            print("✅ Адаптер вызван с правильным режимом 'summary'")
        
        print("🎉 Все тесты обработчиков прошли успешно!")
        
    except Exception as e:
        print(f"❌ Ошибка в тестах обработчиков: {e}")
        import traceback
        traceback.print_exc()

async def main():
    """Главная функция тестирования"""
    print("🚀 Запуск тестов интеграции системы отображения микротем куратора\n")
    
    await test_curator_adapter()
    await test_states()
    await test_handlers()
    
    print("\n🎯 Тестирование завершено!")

if __name__ == "__main__":
    # Добавляем патч для импорта
    from unittest.mock import patch
    
    asyncio.run(main())
